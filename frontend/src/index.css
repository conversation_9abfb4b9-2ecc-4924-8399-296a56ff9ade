@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom gear animation for auto parts theme */
@keyframes gear-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.gear-animation {
  animation: gear-spin 2s linear infinite;
}

/* Smooth gear animation with easing */
@keyframes gear-spin-smooth {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.gear-animation-smooth {
  animation: gear-spin-smooth 3s ease-in-out infinite;
}

/* Auto parts themed colors */
.auto-parts-primary {
  color: #1e40af; /* Blue-700 */
}

.auto-parts-secondary {
  color: #374151; /* Gray-700 */
}

/* Midnight gray admin theme */
.admin-midnight-primary {
  background-color: #212529; /* midnight-800 */
  color: #ffffff; /* Pure white for better visibility */
}

.admin-midnight-secondary {
  background-color: #343a40; /* midnight-700 */
  color: #ffffff; /* Pure white for better visibility */
}

.admin-midnight-accent {
  background-color: #495057; /* midnight-600 */
  color: #ffffff; /* Pure white for better visibility */
}

.admin-midnight-hover {
  background-color: #6c757d; /* midnight-500 */
}

.admin-midnight-card {
  background-color: #1a1d20; /* midnight-900 */
  border-color: #343a40; /* midnight-700 */
}

/* Admin text visibility improvements */
.admin-text-primary {
  color: #ffffff !important; /* Pure white */
}

.admin-text-secondary {
  color: #e9ecef !important; /* Light gray */
}

.admin-text-muted {
  color: #adb5bd !important; /* Medium gray */
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}
